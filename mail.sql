CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT 'Domain của website',
  `type` varchar(255) NOT NULL COMMENT 'login_notification, password_reset, product_delivery',
  `subject` varchar(255) DEFAULT NULL COMMENT 'Tiêu đề email, null = tắt thông báo',
  `content` longtext DEFAULT NULL COMMENT 'Nội dung HTML email',
  `placeholders` json DEFAULT NULL COMMENT 'Danh sách các placeholder có thể dùng',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_templates_domain_type_unique` (`domain`,`type`),
  <PERSON><PERSON><PERSON> `email_templates_domain_status_index` (`domain`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;